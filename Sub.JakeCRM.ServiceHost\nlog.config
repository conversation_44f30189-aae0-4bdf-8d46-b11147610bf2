<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="logs/internal-nlog.txt">

  <!-- Enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Extensions.Logging" />
  </extensions>

  <!-- Define variables -->
  <variable name="logDirectory" value="logs" />
  <variable name="applicationName" value="Sub.JakeCRM.ServiceHost" />
  
  <!-- Define layout patterns -->
  <variable name="defaultLayout" 
            value="${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}" />
  
  <variable name="detailedLayout" 
            value="${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}" />
  
  <variable name="jsonLayout" 
            value='{"timestamp":"${longdate}","level":"${level:uppercase=true}","logger":"${logger}","thread":"${threadid}","message":"${message}","exception":"${exception:format=tostring}","properties":{"application":"${applicationName}","environment":"${environment:ASPNETCORE_ENVIRONMENT}","machineName":"${machinename}","processId":"${processid}"}}' />

  <!-- Define targets -->
  <targets>

    <!-- Console target for immediate feedback -->
    <target xsi:type="Console"
            name="console"
            layout="${time} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}" />

    <!-- Single file target for all application logs -->
    <target xsi:type="File"
            name="logfile"
            fileName="${logDirectory}/app-${shortdate}.log"
            layout="${detailedLayout}"
            archiveFileName="${logDirectory}/archives/app-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="30"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />

  </targets>

  <!-- Define rules -->
  <rules>

    <!-- Skip Microsoft logs and so log only own logs (BlackHole) -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />

    <!-- Application logs to console for immediate feedback -->
    <logger name="Sub.JakeCRM.*" minlevel="Info" writeTo="console" />

    <!-- Application logs (Debug and above) to single file -->
    <logger name="Sub.JakeCRM.*" minlevel="Debug" writeTo="logfile" final="true" />

    <!-- Other system logs (Info and above) to single file -->
    <logger name="*" minlevel="Info" writeTo="logfile" />

  </rules>
  
</nlog>
