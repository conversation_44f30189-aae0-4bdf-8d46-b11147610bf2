using Sub.JakeCRM.Domain.Entities;
using System.Text.Json;

namespace Sub.JakeCRM.Tests;

public class CustomerMessageTests
{
    [Fact]
    public void CustomerMessage_Deserialization_ShouldWorkCorrectly()
    {
        // Arrange
        var json = """
        {
            "id": 123,
            "firstName": "<PERSON>",
            "lastName": "Doe",
            "email": "<EMAIL>"
        }
        """;

        // Act
        var customerMessage = JsonSerializer.Deserialize<CustomerMessage>(json, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        // Assert
        Assert.NotNull(customerMessage);
        Assert.Equal(123, customerMessage.Id);
        Assert.Equal("John", customerMessage.FirstName);
        Assert.Equal("Doe", customerMessage.LastName);
        Assert.Equal("<EMAIL>", customerMessage.Email);
        Assert.True(customerMessage.IsValid());
    }

    [Fact]
    public void CustomerMessage_ToCustomer_ShouldConvertCorrectly()
    {
        // Arrange
        var customerMessage = new CustomerMessage
        {
            Id = 456,
            FirstName = "Jane",
            LastName = "Smith",
            Email = "<EMAIL>"
        };

        // Act
        var customer = customerMessage.ToCustomer();

        // Assert
        Assert.Equal(456, customer.Id);
        Assert.Equal("Jane", customer.FirstName);
        Assert.Equal("Smith", customer.LastName);
        Assert.Equal("<EMAIL>", customer.Email);
        Assert.Equal("Jane Smith", customer.FullName);
        Assert.True(customer.IsValid());
    }

    [Fact]
    public void CustomerMessage_WithInvalidData_ShouldBeInvalid()
    {
        // Arrange
        var customerMessage = new CustomerMessage
        {
            Id = 0, // Invalid ID
            FirstName = "",
            LastName = "",
            Email = ""
        };

        // Act
        var isValid = customerMessage.IsValid();

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void CustomerMessage_CamelCaseDeserialization_ShouldWorkCorrectly()
    {
        // Arrange - JSON with camelCase property names
        var json = """
        {
            "id": 789,
            "firstName": "Bob",
            "lastName": "Johnson",
            "email": "<EMAIL>"
        }
        """;

        // Act
        var customerMessage = JsonSerializer.Deserialize<CustomerMessage>(json);

        // Assert
        Assert.NotNull(customerMessage);
        Assert.Equal(789, customerMessage.Id);
        Assert.Equal("Bob", customerMessage.FirstName);
        Assert.Equal("Johnson", customerMessage.LastName);
        Assert.Equal("<EMAIL>", customerMessage.Email);
    }

    [Fact]
    public void CustomerMessage_PascalCaseDeserialization_ShouldWorkWithCaseInsensitive()
    {
        // Arrange - JSON with PascalCase property names
        var json = """
        {
            "Id": 999,
            "FirstName": "Alice",
            "LastName": "Brown",
            "Email": "<EMAIL>"
        }
        """;

        // Act
        var customerMessage = JsonSerializer.Deserialize<CustomerMessage>(json, new JsonSerializerOptions
        {
            PropertyNameCaseInsensitive = true
        });

        // Assert
        Assert.NotNull(customerMessage);
        Assert.Equal(999, customerMessage.Id);
        Assert.Equal("Alice", customerMessage.FirstName);
        Assert.Equal("Brown", customerMessage.LastName);
        Assert.Equal("<EMAIL>", customerMessage.Email);
    }
}
