using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Diagnostics;
using System.Text.Json;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Service responsible for processing messages from Service Bus
/// </summary>
public class MessageProcessorService : IMessageProcessor
{
    private static readonly JsonSerializerOptions JsonOptions = new()
    {
        PropertyNameCaseInsensitive = true
    };

    private readonly IApiClient _apiClient;
    private readonly ILogger<MessageProcessorService> _logger;

    public MessageProcessorService(IApiClient apiClient, ILogger<MessageProcessorService> logger)
    {
        _apiClient = apiClient ?? throw new ArgumentNullException(nameof(apiClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<ProcessingResult> ProcessMessageAsync(ServiceBusMessage message, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Processing customer message {MessageId}", message.MessageId);

            // Validate message
            if (string.IsNullOrWhiteSpace(message.Body))
            {
                _logger.LogWarning("Message {MessageId} has empty body", message.MessageId);
                return ProcessingResult.Failure("Message body is empty", null, 0,
                    new Dictionary<string, object> { ["MessageId"] = message.MessageId });
            }

            // Process customer message
            return await ProcessCustomerMessageAsync(message, stopwatch, cancellationToken);
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Error processing message {MessageId}", message.MessageId);

            return ProcessingResult.Failure(
                "Unexpected error during message processing",
                ex.ToString(),
                0,
                new Dictionary<string, object> { ["MessageId"] = message.MessageId });
        }
    }



    private async Task<ProcessingResult> ProcessCustomerMessageAsync(ServiceBusMessage message, Stopwatch stopwatch, CancellationToken cancellationToken)
    {
        try
        {
            // Deserialize customer message
            var customerMessage = JsonSerializer.Deserialize<CustomerMessage>(message.Body, JsonOptions);

            if (customerMessage == null || !customerMessage.IsValid())
            {
                _logger.LogWarning("Invalid customer data in message {MessageId}", message.MessageId);
                return ProcessingResult.Failure("Invalid customer data", null, 0,
                    new Dictionary<string, object> { ["MessageId"] = message.MessageId });
            }

            // Convert to Customer domain entity
            var customer = customerMessage.ToCustomer();

            _logger.LogInformation("Processing customer message for customer {CustomerId}: {FirstName} {LastName} ({Email})",
                customer.Id, customer.FirstName, customer.LastName, customer.Email);

            // Use default customer endpoint
            var destinationEndpoint = "/api/customers";

            // Prepare simple headers
            var headers = new Dictionary<string, string>
            {
                ["Content-Type"] = "application/json",
                ["X-Message-Id"] = message.MessageId,
                ["X-Customer-Id"] = customer.Id.ToString(),
                ["X-Customer-Email"] = customer.Email
            };

            // Create the payload for the API call with id, firstName, lastName, email
            var apiPayload = new
            {
                id = customer.Id,
                firstName = customer.FirstName,
                lastName = customer.LastName,
                email = customer.Email
            };

            // Submit to destination API
            var apiResponse = await _apiClient.SubmitAsync(destinationEndpoint, apiPayload, headers, cancellationToken);

            stopwatch.Stop();

            if (apiResponse.IsSuccess)
            {
                _logger.LogInformation("Successfully processed customer message {MessageId} for customer {CustomerId} in {Duration}ms",
                    message.MessageId, customer.Id, stopwatch.ElapsedMilliseconds);

                return ProcessingResult.Success(stopwatch.Elapsed, new Dictionary<string, object>
                {
                    ["MessageId"] = message.MessageId,
                    ["CustomerId"] = customer.Id,
                    ["CustomerEmail"] = customer.Email,
                    ["StatusCode"] = apiResponse.StatusCode,
                    ["ResponseTime"] = apiResponse.ResponseTime.TotalMilliseconds
                });
            }
            else
            {
                _logger.LogError("Failed to process customer message {MessageId} for customer {CustomerId}. Status: {StatusCode}, Error: {Error}",
                    message.MessageId, customer.Id, apiResponse.StatusCode, apiResponse.ErrorMessage);

                return ProcessingResult.Failure(
                    $"Customer API call failed with status {apiResponse.StatusCode}",
                    apiResponse.ErrorMessage,
                    0,
                    new Dictionary<string, object>
                    {
                        ["MessageId"] = message.MessageId,
                        ["CustomerId"] = customer.Id,
                        ["StatusCode"] = apiResponse.StatusCode,
                        ["ResponseContent"] = apiResponse.Content ?? string.Empty
                    });
            }
        }
        catch (JsonException ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Failed to deserialize customer message {MessageId}", message.MessageId);

            return ProcessingResult.Failure(
                "Failed to parse customer message",
                ex.Message,
                0,
                new Dictionary<string, object> { ["MessageId"] = message.MessageId });
        }
    }




}
