namespace Sub.JakeCRM.Infrastructure.Configuration;

/// <summary>
/// Security configuration options
/// </summary>
public class SecurityOptions
{
    public const string SectionName = "Security";

    /// <summary>
    /// Enable data masking for sensitive information in logs
    /// </summary>
    public bool EnableDataMasking { get; set; } = true;

    /// <summary>
    /// Fields to mask in logs (JSON paths or property names)
    /// </summary>
    public List<string> MaskedFields { get; set; } = new()
    {
        "password",
        "secret",
        "token",
        "key",
        "ssn",
        "creditCard"
    };
}
