# Customer Domain Model Implementation

This document describes the implementation of the Customer domain model and Service Bus message processing for customer data.

## Overview

The implementation adds customer-specific processing to the existing Service Bus message processor while maintaining backward compatibility with generic message processing. When a customer message is received, it will be automatically detected, validated, and submitted to the appropriate API endpoint with the correct payload format.

## Components Added

### 1. Customer Domain Model (`Sub.JakeCRM.Domain/Entities/Customer.cs`)

The core customer entity with the following properties:
- `Id` (int) - Unique customer identifier
- `FirstName` (string) - Customer's first name
- `LastName` (string) - Customer's last name  
- `Email` (string) - Customer's email address
- `FullName` (computed) - Returns "FirstName LastName"
- `IsValid()` method - Validates all required fields and email format

### 2. Customer Message DTO (`Sub.JakeCRM.Domain/Entities/CustomerMessage.cs`)

Data transfer object for deserializing Service Bus messages containing customer data:
- Uses `JsonPropertyName` attributes for proper JSON deserialization
- Supports both camelCase and PascalCase property names (case-insensitive)
- `ToCustomer()` method converts to domain entity
- `IsValid()` method validates message data

### 3. Enhanced Message Processor (`Sub.JakeCRM.Application/Services/MessageProcessorService.cs`)

Updated to handle both customer and generic messages:
- **Customer Detection**: Automatically detects customer messages by:
  - Subject containing "customer" or "customers"
  - Message body that can be deserialized as valid customer data
- **Customer Processing**: 
  - Deserializes message body to `CustomerMessage`
  - Validates customer data
  - Converts to domain entity
  - Calls API with structured payload: `{ id, firstName, lastName, email }`
  - Uses `/api/customers` endpoint (or custom endpoint from message properties)
  - Adds customer-specific headers (X-Customer-Id, X-Customer-Email, etc.)
- **Generic Processing**: Falls back to existing generic message processing for non-customer messages

## Message Flow

### Customer Message Processing

1. **Message Reception**: Service Bus message received
2. **Detection**: System checks if message is customer-related
3. **Deserialization**: JSON body parsed as `CustomerMessage`
4. **Validation**: Customer data validated (ID > 0, non-empty names, valid email)
5. **API Call**: POST to `/api/customers` with payload:
   ```json
   {
     "id": 123,
     "firstName": "John", 
     "lastName": "Doe",
     "email": "<EMAIL>"
   }
   ```
6. **Headers**: Additional customer context headers added
7. **Response**: Success/failure result with customer metadata

### Generic Message Processing

Non-customer messages continue to use the existing generic processing logic.

## Expected Message Format

Service Bus messages should contain JSON in the message body:

```json
{
  "id": 12345,
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>"
}
```

### Message Properties

- **Subject**: Can contain "customer" or "customers" to help with detection
- **DestinationEndpoint**: Optional custom API endpoint (defaults to `/api/customers`)
- **ContentType**: Should be "application/json"

## API Integration

The system will make HTTP POST calls to the destination API with:

### Default Endpoint
- `/api/customers` (can be overridden via message properties)

### Payload Format
```json
{
  "id": 123,
  "firstName": "John",
  "lastName": "Doe", 
  "email": "<EMAIL>"
}
```

### Headers
- Standard headers (Content-Type, X-Message-Id, X-Correlation-Id, etc.)
- Customer-specific headers:
  - `X-Customer-Id`: Customer ID
  - `X-Customer-Email`: Customer email
  - `X-Customer-Name`: Full customer name

## Testing

Comprehensive test suite added covering:

### Customer Domain Model Tests (`CustomerTests.cs`)
- Valid customer validation
- Invalid data scenarios (empty names, invalid email, zero ID)
- Email format validation with various test cases

### Customer Message Tests (`CustomerMessageTests.cs`)  
- JSON deserialization (camelCase and PascalCase)
- Conversion to domain entity
- Validation scenarios

### Message Processor Tests (`MessageProcessorServiceTests.cs`)
- Customer message processing with API call verification
- Invalid customer message handling
- Generic message processing (backward compatibility)
- Header validation
- Payload structure verification

## Configuration

No additional configuration required. The system automatically:
- Detects customer messages
- Uses appropriate endpoints
- Maintains existing generic message processing

## Backward Compatibility

The implementation is fully backward compatible:
- Existing generic messages continue to work unchanged
- No breaking changes to existing interfaces
- Additional functionality is additive only

## Error Handling

- Invalid customer data returns failure result without API call
- JSON deserialization errors are caught and logged
- API failures are properly logged with customer context
- Retry logic follows existing patterns

## Performance Optimizations

- Static `JsonSerializerOptions` instance to avoid repeated allocations
- Efficient customer detection logic
- Minimal overhead for non-customer messages
