using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text.Json;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Implementation of configuration service
/// </summary>
public class ConfigurationService : IConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<ConfigurationService> _logger;

    public ConfigurationService(
        IConfiguration configuration,
        ILogger<ConfigurationService> logger)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));


    }

    public T GetValue<T>(string key, T defaultValue = default!)
    {
        try
        {
            return _configuration.GetValue<T>(key, defaultValue) ?? defaultValue;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get configuration value for key: {Key}. Using default value: {DefaultValue}", key, defaultValue);
            return defaultValue;
        }
    }

    public Dictionary<string, string> GetSection(string sectionName)
    {
        var section = _configuration.GetSection(sectionName);
        var result = new Dictionary<string, string>();

        foreach (var kvp in section.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                result[kvp.Key] = kvp.Value;
            }
        }

        return result;
    }

    public T GetOptions<T>() where T : class, new()
    {
        var options = new T();
        var sectionName = GetSectionNameForType<T>();

        if (!string.IsNullOrEmpty(sectionName))
        {
            _configuration.GetSection(sectionName).Bind(options);
        }

        return options;
    }

    public bool HasKey(string key)
    {
        return _configuration[key] != null;
    }

    public Dictionary<string, string> GetAllSettings()
    {
        var settings = new Dictionary<string, string>();

        foreach (var kvp in _configuration.AsEnumerable())
        {
            if (kvp.Value != null)
            {
                settings[kvp.Key] = MaskSensitiveValue(kvp.Key, kvp.Value);
            }
        }

        return settings;
    }

    public ConfigurationValidationResult ValidateConfiguration()
    {
        var result = new ConfigurationValidationResult();

        // Validate critical configuration values
        var serviceBusConnectionString = _configuration["ServiceBus:ConnectionString"];
        if (string.IsNullOrWhiteSpace(serviceBusConnectionString))
        {
            result.Errors.Add("ServiceBus:ConnectionString is required");
        }

        var queueName = _configuration["ServiceBus:QueueName"];
        var topicName = _configuration["ServiceBus:TopicName"];
        var subscriptionName = _configuration["ServiceBus:SubscriptionName"];

        if (string.IsNullOrWhiteSpace(queueName) &&
            (string.IsNullOrWhiteSpace(topicName) || string.IsNullOrWhiteSpace(subscriptionName)))
        {
            result.Errors.Add("Either ServiceBus:QueueName or both ServiceBus:TopicName and ServiceBus:SubscriptionName must be specified");
        }

        var apiBaseUrl = _configuration["ApiClient:BaseUrl"];
        if (string.IsNullOrWhiteSpace(apiBaseUrl))
        {
            result.Errors.Add("ApiClient:BaseUrl is required");
        }
        else if (!Uri.TryCreate(apiBaseUrl, UriKind.Absolute, out _))
        {
            result.Errors.Add("ApiClient:BaseUrl must be a valid URL");
        }

        result.IsValid = result.Errors.Count == 0;
        return result;
    }

    public void ReloadConfiguration()
    {
        try
        {
            if (_configuration is IConfigurationRoot configRoot)
            {
                configRoot.Reload();
                _logger.LogInformation("Configuration reloaded successfully");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to reload configuration");
        }
    }



    private string GetSectionNameForType<T>()
    {
        // Use convention-based mapping: remove "Options" suffix from type name
        var typeName = typeof(T).Name;
        if (typeName.EndsWith("Options"))
        {
            return typeName[..^7]; // Remove "Options" suffix
        }
        return typeName;
    }

    private string MaskSensitiveValue(string key, string value)
    {
        var sensitiveKeys = new[] { "password", "secret", "key", "token", "connectionstring" };
        
        if (sensitiveKeys.Any(k => key.ToLowerInvariant().Contains(k)))
        {
            return value.Length > 4 ? $"{value[..4]}****" : "****";
        }

        return value;
    }


}
