2025-06-07 18:29:22.1169 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 18:29:22.1521 DEBUG [TestLogging] [1] This is a DEBUG message 
2025-06-07 18:29:22.1521 INFO  [TestLogging] [1] This is an INFO message 
2025-06-07 18:29:22.1521 WARN  [TestLogging] [1] This is a WARN message 
2025-06-07 18:29:22.1521 ERROR [TestLogging] [1] This is an ERROR message 
2025-06-07 18:29:22.1521 FATAL [TestLogging] [1] This is a FATAL message 
2025-06-07 18:29:22.1767 ERROR [TestLogging] [1] Caught test exception System.InvalidOperationException: Test exception for logging
   at Sub.JakeCRM.ServiceHost.TestLogging.TestAllLogLevels() in C:\Users\<USER>\source\repos\Sub.JakeCRM\Sub.JakeCRM.ServiceHost\TestLogging.cs:line 25
2025-06-07 18:29:22.2129 INFO  [TestLogging] [1] Processing message "MSG-12345" for customer "CUST-67890" at 6/7/2025 11:29:22 PM 
2025-06-07 18:29:22.6143 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 18:29:22.7580 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 18:29:22.7580 INFO  [ConfigurationService] [1] Configuration Summary: 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Application: Sub.JakeCRM.ServiceHost v1.0.0 (Production) 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Service Bus: Queue=your-queue-name, Topic=, Subscription= 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] API Client: BaseUrl=https://your-destination-api.com, Timeout=00:00:30, RetryCount=3 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Processing: Batching=False, Deduplication=False, Parallel=True 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Monitoring: AppInsights=False, Metrics=True, HealthChecks=True 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Security: Encryption=False, KeyVault=False, Audit=True 
2025-06-07 18:29:22.7729 INFO  [ConfigurationService] [1] Configuration validation passed successfully 
2025-06-07 18:29:22.7729 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 18:29:22.7729 INFO  [ConfigurationValidationService] [1] === Configuration Summary === 
2025-06-07 18:29:22.8005 INFO  [ConfigurationValidationService] [1] Application:Name: Sub.JakeCRM.ServiceHost 
2025-06-07 18:29:22.8005 INFO  [ConfigurationValidationService] [1] Application:Version: 1.0.0 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Application:Environment: Production 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ServiceBus:QueueName: your-queue-name 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ServiceBus:TopicName:  
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ServiceBus:MaxConcurrentCalls: 1 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ApiClient:BaseUrl: https://your-destination-api.com 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ApiClient:Timeout: 00:00:30 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] ApiClient:RetryCount: 3 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Processing:EnableBatching: False 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Processing:EnableParallelProcessing: True 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Monitoring:EnableCustomMetrics: True 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Monitoring:HealthChecks:Enabled: True 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Security:EnableEncryption: False 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] Security:EnableAuditLogging: True 
2025-06-07 18:29:22.8028 INFO  [ConfigurationValidationService] [1] === End Configuration Summary === 
2025-06-07 18:29:22.8028 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 18:29:22.8256 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 18:29:22.8771 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 18:29:22.9940 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 18:29:22.9940 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:14:53.9865 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:14:54.0492 FATAL [Program] [1] Sub.JakeCRM Service Host terminated unexpectedly System.IO.FileNotFoundException: The configuration file 'appsettings.json' was not found and is not optional. The expected physical path was 'C:\Users\<USER>\source\repos\Sub.JakeCRM\appsettings.json'.
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.HandleException(ExceptionDispatchInfo info)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load(Boolean reload)
   at Microsoft.Extensions.Configuration.FileConfigurationProvider.Load()
   at Microsoft.Extensions.Configuration.ConfigurationRoot..ctor(IList`1 providers)
   at Microsoft.Extensions.Configuration.ConfigurationBuilder.Build()
   at Microsoft.Extensions.Hosting.HostBuilder.InitializeAppConfiguration()
   at Microsoft.Extensions.Hosting.HostBuilder.Build()
   at Sub.JakeCRM.ServiceHost.Program.Main(String[] args) in C:\Users\<USER>\source\repos\Sub.JakeCRM\Sub.JakeCRM.ServiceHost\Program.cs:line 27
2025-06-07 20:15:00.1787 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:15:00.3743 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 20:15:00.4487 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 20:15:00.4487 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 20:15:00.4537 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 20:15:00.4644 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 20:15:00.4949 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 20:15:00.5681 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 20:15:00.5690 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:15:07.9299 INFO  [MessageProcessingBackgroundService] [5] Message Processing Background Service stopping... 
2025-06-07 20:15:07.9299 INFO  [ServiceBusReceiver] [5] Stopping Service Bus receiver... 
2025-06-07 20:15:07.9299 INFO  [ServiceBusReceiver] [11] Service Bus receiver stopped 
2025-06-07 20:15:07.9299 INFO  [MessageProcessingBackgroundService] [10] Message Processing Background Service is stopping due to cancellation 
2025-06-07 20:15:07.9456 INFO  [MessageProcessingBackgroundService] [10] Message Processing Background Service stopped 
2025-06-07 20:15:18.5824 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:15:18.7116 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 20:15:18.7889 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 20:15:18.7889 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 20:15:18.7889 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 20:15:18.7889 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 20:15:18.8267 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 20:15:18.8953 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 20:15:18.8953 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:15:25.0754 INFO  [MessageProcessingBackgroundService] [5] Message Processing Background Service stopping... 
2025-06-07 20:15:25.0754 INFO  [ServiceBusReceiver] [5] Stopping Service Bus receiver... 
2025-06-07 20:15:25.0754 INFO  [ServiceBusReceiver] [12] Service Bus receiver stopped 
2025-06-07 20:15:25.0847 INFO  [MessageProcessingBackgroundService] [13] Message Processing Background Service is stopping due to cancellation 
2025-06-07 20:15:25.0847 INFO  [MessageProcessingBackgroundService] [13] Message Processing Background Service stopped 
2025-06-07 20:15:48.0104 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:15:48.1666 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 20:15:48.2321 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 20:15:48.2321 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 20:15:48.2321 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 20:15:48.2321 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 20:15:48.2647 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 20:15:48.3324 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 20:15:48.3324 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:15:52.9657 INFO  [MessageProcessingBackgroundService] [5] Message Processing Background Service stopping... 
2025-06-07 20:15:52.9657 INFO  [ServiceBusReceiver] [5] Stopping Service Bus receiver... 
2025-06-07 20:15:52.9755 INFO  [ServiceBusReceiver] [11] Service Bus receiver stopped 
2025-06-07 20:15:52.9755 INFO  [MessageProcessingBackgroundService] [12] Message Processing Background Service is stopping due to cancellation 
2025-06-07 20:15:52.9755 INFO  [MessageProcessingBackgroundService] [12] Message Processing Background Service stopped 
2025-06-07 20:17:12.8002 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:17:13.2696 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 20:17:13.4343 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 20:17:13.4343 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 20:17:13.4343 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 20:17:13.4668 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 20:17:13.5180 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 20:17:13.6639 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 20:17:13.6639 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:20:22.3920 INFO  [Program] [1] Sub.JakeCRM Service Host initializing... 
2025-06-07 20:20:22.9962 INFO  [Program] [1] Sub.JakeCRM Service Host starting... 
2025-06-07 20:20:23.1447 INFO  [ConfigurationValidationService] [1] Starting configuration validation... 
2025-06-07 20:20:23.1480 INFO  [ConfigurationValidationService] [1] Configuration validation completed successfully 
2025-06-07 20:20:23.1480 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service starting... 
2025-06-07 20:20:23.1480 INFO  [ServiceBusReceiver] [1] Starting Service Bus receiver... 
2025-06-07 20:20:23.2112 INFO  [ServiceBusReceiver] [1] Created processor for queue: your-queue-name 
2025-06-07 20:20:23.3430 INFO  [ServiceBusReceiver] [1] Service Bus receiver started successfully 
2025-06-07 20:20:23.3430 INFO  [MessageProcessingBackgroundService] [1] Message Processing Background Service started successfully 
2025-06-07 20:20:36.0178 ERROR [ServiceBusReceiver] [9] Service Bus processor error. Source: Receive, Entity: your-queue-name Azure.Messaging.ServiceBus.ServiceBusException: No such host is known. ErrorCode: HostNotFound (ServiceCommunicationProblem). For troubleshooting information, see https://aka.ms/azsdk/net/servicebus/exceptions/troubleshoot.
 ---> System.Net.Sockets.SocketException (11001): No such host is known.
   at Microsoft.Azure.Amqp.ExceptionDispatcher.Throw(Exception exception)
   at Microsoft.Azure.Amqp.AsyncResult.End[TAsyncResult](IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.ConnectAsyncResult.End(IAsyncResult result)
   at Microsoft.Azure.Amqp.Transport.AmqpTransportInitiator.<>c.<ConnectAsync>b__17_1(IAsyncResult r)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.CreateAndOpenConnectionAsync(Version amqpVersion, Uri serviceEndpoint, Uri connectionEndpoint, ServiceBusTransportType transportType, IWebProxy proxy, RemoteCertificateValidationCallback certificateValidationCallback, String scopeIdentifier, TimeSpan timeout)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpConnectionScope.OpenReceiverLinkAsync(String identifier, String entityPath, TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String sessionId, Boolean isSessionReceiver, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.OpenReceiverLinkAsync(TimeSpan timeout, UInt32 prefetchCount, ServiceBusReceiveMode receiveMode, String identifier, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.FaultTolerantAmqpObject`1.OnCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Microsoft.Azure.Amqp.Singleton`1.GetOrCreateAsync(TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   --- End of inner exception stack trace ---
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsyncInternal(Int32 maxMessages, Nullable`1 maxWaitTime, TimeSpan timeout, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.<>c.<<ReceiveMessagesAsync>b__44_0>d.MoveNext()
--- End of stack trace from previous location ---
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.ServiceBusRetryPolicy.RunOperation[T1,TResult](Func`4 operation, T1 t1, TransportConnectionScope scope, CancellationToken cancellationToken, Boolean logTimeoutRetriesAsVerbose)
   at Azure.Messaging.ServiceBus.Amqp.AmqpReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ServiceBusReceiver.ReceiveMessagesAsync(Int32 maxMessages, Nullable`1 maxWaitTime, Boolean isProcessor, CancellationToken cancellationToken)
   at Azure.Messaging.ServiceBus.ReceiverManager.ReceiveAndProcessMessagesAsync(CancellationToken cancellationToken)
