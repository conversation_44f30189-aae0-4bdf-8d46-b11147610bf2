namespace Sub.JakeCRM.Infrastructure.Configuration;

/// <summary>
/// General application configuration options
/// </summary>
public class ApplicationOptions
{
    public const string SectionName = "Application";

    /// <summary>
    /// Application name for logging and identification
    /// </summary>
    public string Name { get; set; } = "Sub.JakeCRM.ServiceHost";

    /// <summary>
    /// Application version
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// Application environment (Development, Staging, Production)
    /// </summary>
    public string Environment { get; set; } = "Production";

    /// <summary>
    /// Graceful shutdown timeout in seconds
    /// </summary>
    public int ShutdownTimeoutSeconds { get; set; } = 30;
}
