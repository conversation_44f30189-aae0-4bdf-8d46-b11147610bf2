<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
      xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
      autoReload="true"
      internalLogLevel="Info"
      internalLogFile="logs/internal-nlog-dev.txt">

  <!-- Enable asp.net core layout renderers -->
  <extensions>
    <add assembly="NLog.Extensions.Logging" />
  </extensions>

  <!-- Define variables -->
  <variable name="logDirectory" value="logs" />
  <variable name="applicationName" value="Sub.JakeCRM.ServiceHost" />
  
  <!-- Define layout patterns for development -->
  <variable name="consoleLayout" 
            value="${time:format=HH\:mm\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}" />
  
  <variable name="fileLayout" 
            value="${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}" />

  <!-- Define targets for development -->
  <targets>

    <!-- Console target with colors for development -->
    <target xsi:type="ColoredConsole"
            name="console"
            layout="${consoleLayout}"
            useDefaultRowHighlightingRules="false">
      <highlight-row condition="level == LogLevel.Debug" foregroundColor="DarkGray" />
      <highlight-row condition="level == LogLevel.Info" foregroundColor="Gray" />
      <highlight-row condition="level == LogLevel.Warn" foregroundColor="Yellow" />
      <highlight-row condition="level == LogLevel.Error" foregroundColor="Red" />
      <highlight-row condition="level == LogLevel.Fatal" foregroundColor="Red" backgroundColor="White" />
    </target>

    <!-- Single file target for all development logs -->
    <target xsi:type="File"
            name="logfile"
            fileName="${logDirectory}/app-${shortdate}.log"
            layout="${fileLayout}"
            archiveFileName="${logDirectory}/archives/app-{#}.log"
            archiveEvery="Day"
            archiveNumbering="Rolling"
            maxArchiveFiles="7"
            concurrentWrites="true"
            keepFileOpen="false"
            createDirs="true" />

    <!-- Debug output for Visual Studio -->
    <target xsi:type="Debugger"
            name="debugger"
            layout="${time:format=HH\:mm\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message}" />

  </targets>

  <!-- Define rules for development -->
  <rules>

    <!-- Skip Microsoft logs below Warning level -->
    <logger name="Microsoft.*" maxlevel="Info" final="true" />
    <logger name="System.Net.Http.*" maxlevel="Info" final="true" />

    <!-- All application logs to console -->
    <logger name="Sub.JakeCRM.*" minlevel="Debug" writeTo="console" />

    <!-- All application logs to single file -->
    <logger name="Sub.JakeCRM.*" minlevel="Debug" writeTo="logfile" final="true" />

    <!-- Other system logs (Info and above) to single file -->
    <logger name="*" minlevel="Info" writeTo="logfile" />

    <!-- All logs to debugger output -->
    <logger name="*" minlevel="Debug" writeTo="debugger" />

  </rules>
  
</nlog>
