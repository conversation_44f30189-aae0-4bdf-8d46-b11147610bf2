using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using NLog;
using NLog.Config;
using NLog.Targets;
using NLog.Targets.Wrappers;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text;

namespace Sub.JakeCRM.Application.Services;

/// <summary>
/// Implementation of NLog configuration service
/// </summary>
public class NLogConfigurationService : INLogConfigurationService
{
    private readonly IConfiguration _configuration;
    private readonly IHostEnvironment _environment;
    private readonly Logger _logger;

    public NLogConfigurationService(
        IConfiguration configuration,
        IHostEnvironment environment)
    {
        _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        _environment = environment ?? throw new ArgumentNullException(nameof(environment));
        _logger = LogManager.GetCurrentClassLogger();
    }

    public void ConfigureNLog()
    {
        try
        {
            _logger.Info("Configuring NLog with application settings...");

            var config = new LoggingConfiguration();

            // Configure variables
            ConfigureVariables(config);

            // Configure targets
            ConfigureTargets(config);

            // Configure rules
            ConfigureRules(config);

            // Apply configuration
            LogManager.Configuration = config;

            _logger.Info("NLog configuration completed successfully");
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to configure NLog");
            throw;
        }
    }

    public void ReconfigureNLog()
    {
        _logger.Info("Reconfiguring NLog...");
        ConfigureNLog();
    }

    public void AddCustomTarget(string name, object target)
    {
        try
        {
            if (target is Target nlogTarget)
            {
                var config = LogManager.Configuration ?? new LoggingConfiguration();
                config.AddTarget(name, nlogTarget);
                LogManager.Configuration = config;
                _logger.Debug("Added custom target: {TargetName}", name);
            }
            else
            {
                _logger.Error("Target must be of type NLog.Targets.Target");
            }
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to add custom target: {TargetName}", name);
        }
    }

    public void AddLoggingRule(string loggerNamePattern, string minLevel, string targetName)
    {
        try
        {
            var config = LogManager.Configuration ?? new LoggingConfiguration();
            var logLevel = GetLogLevel(minLevel);
            var rule = new LoggingRule(loggerNamePattern, logLevel, config.FindTargetByName(targetName));
            config.LoggingRules.Add(rule);
            LogManager.Configuration = config;
            _logger.Debug("Added logging rule: {Pattern} -> {Target}", loggerNamePattern, targetName);
        }
        catch (Exception ex)
        {
            _logger.Error(ex, "Failed to add logging rule: {Pattern} -> {Target}", loggerNamePattern, targetName);
        }
    }

    private void ConfigureVariables(LoggingConfiguration config)
    {
        config.Variables["logDirectory"] = _configuration["NLog:LogDirectory"] ?? "logs";
        config.Variables["applicationName"] = _configuration["Application:Name"] ?? "Sub.JakeCRM.ServiceHost";
        config.Variables["environment"] = _environment.EnvironmentName;
        config.Variables["version"] = _configuration["Application:Version"] ?? "1.0.0";
    }

    private void ConfigureTargets(LoggingConfiguration config)
    {
        // Console target
        if (GetConfigBool("NLog:EnableConsoleLogging", true))
        {
            var consoleTarget = CreateConsoleTarget();
            config.AddTarget("console", consoleTarget);
        }

        // Single file target for all logs
        if (GetConfigBool("NLog:EnableFileLogging", true))
        {
            var fileTarget = CreateFileTarget("logfile", "app-${shortdate}.log");
            config.AddTarget("logfile", fileTarget);
        }
    }

    private void ConfigureRules(LoggingConfiguration config)
    {
        // Skip Microsoft logs to reduce noise
        var microsoftRule = new LoggingRule("Microsoft.*", LogLevel.Warn, null) { Final = true };
        config.LoggingRules.Add(microsoftRule);

        var httpRule = new LoggingRule("System.Net.Http.*", LogLevel.Warn, null) { Final = true };
        config.LoggingRules.Add(httpRule);

        // Application logs to console
        if (GetConfigBool("NLog:EnableConsoleLogging", true) && config.FindTargetByName("console") != null)
        {
            var consoleMinLevel = _configuration["NLog:ConsoleMinLevel"] ?? "Info";
            var consoleRule = new LoggingRule("Sub.JakeCRM.*", GetLogLevel(consoleMinLevel), config.FindTargetByName("console"));
            config.LoggingRules.Add(consoleRule);
        }

        // Application logs to file
        if (GetConfigBool("NLog:EnableFileLogging", true) && config.FindTargetByName("logfile") != null)
        {
            var fileMinLevel = _configuration["NLog:FileMinLevel"] ?? "Info";
            var fileRule = new LoggingRule("*", GetLogLevel(fileMinLevel), config.FindTargetByName("logfile"));
            config.LoggingRules.Add(fileRule);
        }
    }

    private Target CreateConsoleTarget()
    {
        var target = new ColoredConsoleTarget("console")
        {
            Layout = "${time:format=HH\\:mm\\:ss.fff} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] ${message} ${exception:format=tostring}"
        };

        // Add color rules
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Debug", ConsoleOutputColor.DarkGray, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Info", ConsoleOutputColor.Gray, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Warn", ConsoleOutputColor.Yellow, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Error", ConsoleOutputColor.Red, ConsoleOutputColor.NoChange));
        target.RowHighlightingRules.Add(new ConsoleRowHighlightingRule("level == LogLevel.Fatal", ConsoleOutputColor.Red, ConsoleOutputColor.White));

        return target;
    }

    private FileTarget CreateFileTarget(string name, string fileName)
    {
        var logDirectory = _configuration["NLog:LogDirectory"] ?? "logs";
        return new FileTarget(name)
        {
            FileName = $"{logDirectory}/{fileName}",
            Layout = "${longdate} ${level:uppercase=true:padding=-5} [${logger:shortName=true}] [${threadid}] ${message} ${exception:format=tostring}",
            ArchiveFileName = $"{logDirectory}/archives/{name}-{{#}}.log",
            ArchiveEvery = GetFileArchivePeriod(_configuration["NLog:ArchiveEvery"] ?? "Day"),
            ArchiveNumbering = ArchiveNumberingMode.Rolling,
            MaxArchiveFiles = GetConfigInt("NLog:MaxArchiveFiles", 30),
            ConcurrentWrites = GetConfigBool("NLog:EnableConcurrentWrites", true),
            KeepFileOpen = GetConfigBool("NLog:KeepFileOpen", false),
            Encoding = Encoding.GetEncoding(_configuration["NLog:FileEncoding"] ?? "UTF-8"),
            BufferSize = GetConfigInt("NLog:FileBufferSize", 32768),
            AutoFlush = true,
            EnableArchiveFileCompression = GetConfigBool("NLog:EnableCompression", false)
        };
    }



    private static LogLevel GetLogLevel(string level)
    {
        return level.ToLowerInvariant() switch
        {
            "trace" => LogLevel.Trace,
            "debug" => LogLevel.Debug,
            "info" => LogLevel.Info,
            "warn" => LogLevel.Warn,
            "error" => LogLevel.Error,
            "fatal" => LogLevel.Fatal,
            "off" => LogLevel.Off,
            _ => LogLevel.Info
        };
    }

    private static FileArchivePeriod GetFileArchivePeriod(string period)
    {
        return period.ToLowerInvariant() switch
        {
            "minute" => FileArchivePeriod.Minute,
            "hour" => FileArchivePeriod.Hour,
            "day" => FileArchivePeriod.Day,
            "month" => FileArchivePeriod.Month,
            "year" => FileArchivePeriod.Year,
            _ => FileArchivePeriod.Day
        };
    }

    private bool GetConfigBool(string key, bool defaultValue)
    {
        var value = _configuration[key];
        return bool.TryParse(value, out var result) ? result : defaultValue;
    }

    private int GetConfigInt(string key, int defaultValue)
    {
        var value = _configuration[key];
        return int.TryParse(value, out var result) ? result : defaultValue;
    }
}
