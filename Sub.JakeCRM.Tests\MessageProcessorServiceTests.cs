using Microsoft.Extensions.Logging;
using Moq;
using Sub.JakeCRM.Application.Services;
using Sub.JakeCRM.Domain.Entities;
using Sub.JakeCRM.Domain.Interfaces;
using System.Text.Json;

namespace Sub.JakeCRM.Tests;

public class MessageProcessorServiceTests
{
    private readonly Mock<IApiClient> _mockApiClient;
    private readonly Mock<ILogger<MessageProcessorService>> _mockLogger;
    private readonly MessageProcessorService _messageProcessor;

    public MessageProcessorServiceTests()
    {
        _mockApiClient = new Mock<IApiClient>();
        _mockLogger = new Mock<ILogger<MessageProcessorService>>();
        _messageProcessor = new MessageProcessorService(_mockApiClient.Object, _mockLogger.Object);
    }

    [Fact]
    public async Task ProcessMessageAsync_WithValidCustomerMessage_ShouldCallApiWithCorrectData()
    {
        // Arrange
        var customerData = new
        {
            id = 123,
            firstName = "John",
            lastName = "Doe",
            email = "<EMAIL>"
        };

        var serviceBusMessage = new ServiceBusMessage
        {
            MessageId = "test-message-123",
            Body = JsonSerializer.Serialize(customerData)
        };

        var expectedApiResponse = new ApiResponse
        {
            IsSuccess = true,
            StatusCode = 200,
            Content = "Success",
            ResponseTime = TimeSpan.FromMilliseconds(100)
        };

        _mockApiClient
            .Setup(x => x.SubmitAsync(
                "/api/customers",
                It.Is<object>(data => ValidateCustomerApiPayload(data, customerData)),
                It.IsAny<Dictionary<string, string>>(),
                It.IsAny<CancellationToken>()))
            .ReturnsAsync(expectedApiResponse);

        // Act
        var result = await _messageProcessor.ProcessMessageAsync(serviceBusMessage);

        // Assert
        Assert.True(result.IsSuccess);
        Assert.Equal("test-message-123", result.Metadata["MessageId"]);
        Assert.Equal(123, result.Metadata["CustomerId"]);
        Assert.Equal("<EMAIL>", result.Metadata["CustomerEmail"]);
        Assert.Equal(200, result.Metadata["StatusCode"]);

        _mockApiClient.Verify(x => x.SubmitAsync(
            "/api/customers",
            It.IsAny<object>(),
            It.Is<Dictionary<string, string>>(headers =>
                headers.ContainsKey("X-Customer-Id") &&
                headers["X-Customer-Id"] == "123" &&
                headers.ContainsKey("X-Customer-Email") &&
                headers["X-Customer-Email"] == "<EMAIL>"),
            It.IsAny<CancellationToken>()), Times.Once);
    }

    [Fact]
    public async Task ProcessMessageAsync_WithInvalidCustomerMessage_ShouldReturnFailure()
    {
        // Arrange
        var invalidCustomerData = new
        {
            id = 0, // Invalid ID
            firstName = "",
            lastName = "",
            email = "invalid-email"
        };

        var serviceBusMessage = new ServiceBusMessage
        {
            MessageId = "test-message-456",
            Body = JsonSerializer.Serialize(invalidCustomerData)
        };

        // Act
        var result = await _messageProcessor.ProcessMessageAsync(serviceBusMessage);

        // Assert
        Assert.False(result.IsSuccess);
        Assert.Equal("Invalid customer data", result.ErrorMessage);
        Assert.Equal("test-message-456", result.Metadata["MessageId"]);

        _mockApiClient.Verify(x => x.SubmitAsync(
            It.IsAny<string>(),
            It.IsAny<object>(),
            It.IsAny<Dictionary<string, string>>(),
            It.IsAny<CancellationToken>()), Times.Never);
    }

    private static bool ValidateCustomerApiPayload(object payload, object expectedData)
    {
        var payloadJson = JsonSerializer.Serialize(payload);
        var expectedJson = JsonSerializer.Serialize(expectedData);
        return payloadJson == expectedJson;
    }
}
