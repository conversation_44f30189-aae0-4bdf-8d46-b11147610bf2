namespace Sub.JakeCRM.Infrastructure.Configuration;

/// <summary>
/// Monitoring and observability configuration options
/// </summary>
public class MonitoringOptions
{
    public const string SectionName = "Monitoring";



    /// <summary>
    /// Health check endpoints configuration
    /// </summary>
    public HealthCheckOptions HealthChecks { get; set; } = new();
}

/// <summary>
/// Health check configuration options
/// </summary>
public class HealthCheckOptions
{
    /// <summary>
    /// Enable health check endpoint
    /// </summary>
    public bool Enabled { get; set; } = true;

    /// <summary>
    /// Health check endpoint path
    /// </summary>
    public string Path { get; set; } = "/health";

    /// <summary>
    /// Detailed health check endpoint path
    /// </summary>
    public string DetailedPath { get; set; } = "/health/detailed";

    /// <summary>
    /// Health check timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 30;

    /// <summary>
    /// Include dependency health checks
    /// </summary>
    public bool IncludeDependencies { get; set; } = true;

    /// <summary>
    /// Dependency health check configurations
    /// </summary>
    public List<DependencyHealthCheck> Dependencies { get; set; } = new();
}

/// <summary>
/// Dependency health check configuration
/// </summary>
public class DependencyHealthCheck
{
    /// <summary>
    /// Dependency name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Dependency type (ServiceBus, Api, Database, etc.)
    /// </summary>
    public string Type { get; set; } = string.Empty;

    /// <summary>
    /// Connection string or endpoint for health check
    /// </summary>
    public string ConnectionString { get; set; } = string.Empty;

    /// <summary>
    /// Health check timeout in seconds
    /// </summary>
    public int TimeoutSeconds { get; set; } = 10;

    /// <summary>
    /// Whether this dependency is critical for application health
    /// </summary>
    public bool IsCritical { get; set; } = true;

    /// <summary>
    /// Custom health check parameters
    /// </summary>
    public Dictionary<string, string> Parameters { get; set; } = new();
}
