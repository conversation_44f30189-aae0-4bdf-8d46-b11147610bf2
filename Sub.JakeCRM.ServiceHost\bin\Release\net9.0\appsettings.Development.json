{"Logging": {"LogLevel": {"Default": "Debug", "Microsoft": "Information", "Microsoft.Hosting.Lifetime": "Information", "Azure.Messaging.ServiceBus": "Information", "System.Net.Http.HttpClient": "Information"}}, "NLog": {"AutoReload": true, "InternalLogLevel": "Debug", "EnableInternalLogging": true, "InternalLogFile": "logs/internal-nlog-dev.txt", "LogDirectory": "logs", "MaxArchiveFiles": 7, "ArchiveEvery": "Day", "EnableConsoleLogging": true, "EnableFileLogging": true, "EnableEventLogLogging": false, "EnableJsonLogging": false, "EnableDatabaseLogging": false, "EnablePerformanceCounters": false, "ConsoleMinLevel": "Debug", "FileMinLevel": "Debug", "EventLogMinLevel": "Error", "JsonMinLevel": "Info", "EnableConcurrentWrites": true, "KeepFileOpen": false, "EnableCompression": false, "FileEncoding": "UTF-8", "FileBufferSize": 8192, "FlushTimeoutMs": 500, "EnableAsyncLogging": false, "CustomTargets": [], "LoggerRules": []}, "Application": {"Name": "Sub.JakeCRM.ServiceHost", "Version": "1.0.0-dev", "Environment": "Development", "EnablePerformanceMonitoring": true, "EnableDetailedLogging": true, "MaxMessagesPerMinute": 0, "ShutdownTimeoutSeconds": 10, "EnableHealthChecks": true, "HealthCheckIntervalSeconds": 15, "CustomProperties": {"DeploymentRegion": "Local", "ServiceTier": "Development"}}, "ServiceBus": {"ConnectionString": "Endpoint=sb://localhost;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-dev-key", "QueueName": "dev-queue"}, "ApiClient": {"BaseUrl": "https://localhost:7001", "Timeout": "00:00:10", "RetryCount": 1, "RetryDelay": "00:00:01", "EnableDetailedLogging": true, "MaxLogContentLength": 8192}, "Processing": {"EnableMessageTransformation": false, "EnableMessageFiltering": false, "EnableBatching": false, "EnableDeduplication": false, "EnableArchiving": false, "ProcessingTimeoutSeconds": 60, "EnableParallelProcessing": false, "MaxDegreeOfParallelism": 1}, "Monitoring": {"EnableApplicationInsights": false, "EnableCustomMetrics": true, "MetricsIntervalSeconds": 30, "EnablePerformanceCounters": true, "EnableDistributedTracing": false, "EnableStructuredLogging": true, "EnableAlerting": false, "HealthChecks": {"Enabled": true, "TimeoutSeconds": 10, "IncludeDependencies": false}}, "Security": {"EnableEncryption": false, "EnableMessageSigning": false, "EnableApiAuthentication": false, "EnableCertificateAuthentication": false, "EnableKeyVault": false, "EnableAuditLogging": false, "EnableDataMasking": true, "EnableIpFiltering": false, "EnableRateLimiting": false}}