# Configuration Guide

This document describes all available configuration options for the Sub.JakeCRM Service Host application.

## Configuration Sources

The application loads configuration from multiple sources in the following order (later sources override earlier ones):

1. `appsettings.json` - Base configuration
2. `appsettings.{Environment}.json` - Environment-specific configuration
3. Environment variables
4. Command line arguments

## Configuration Sections

### Application Settings

Controls general application behavior.

```json
{
  "Application": {
    "Name": "Sub.JakeCRM.ServiceHost",
    "Version": "1.0.0",
    "Environment": "Production",
    "EnablePerformanceMonitoring": true,
    "EnableDetailedLogging": false,
    "MaxMessagesPerMinute": 0,
    "ShutdownTimeoutSeconds": 30,
    "EnableHealthChecks": true,
    "HealthCheckIntervalSeconds": 30,
    "CustomProperties": {
      "DeploymentRegion": "East US",
      "ServiceTier": "Standard"
    }
  }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `Name` | string | "Sub.JakeCRM.ServiceHost" | Application name for logging |
| `Version` | string | "1.0.0" | Application version |
| `Environment` | string | "Production" | Environment name |
| `EnablePerformanceMonitoring` | bool | true | Enable performance metrics |
| `EnableDetailedLogging` | bool | false | Enable verbose logging |
| `MaxMessagesPerMinute` | int | 0 | Rate limit (0 = unlimited) |
| `ShutdownTimeoutSeconds` | int | 30 | Graceful shutdown timeout |
| `EnableHealthChecks` | bool | true | Enable health check endpoints |
| `HealthCheckIntervalSeconds` | int | 30 | Health check frequency |
| `CustomProperties` | object | {} | Custom key-value properties |

### Service Bus Settings

Configures Azure Service Bus connection for customer message processing.

```json
{
  "ServiceBus": {
    "ConnectionString": "Endpoint=sb://...",
    "QueueName": "your-queue-name"
  }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `ConnectionString` | string | **Required** | Service Bus connection string |
| `QueueName` | string | **Required*** | Queue name to receive from |
| `TopicName` | string | null | Topic name (alternative to queue) |
| `SubscriptionName` | string | null | Subscription name (required with topic) |
| `MaxConcurrentCalls` | int | 1 | Max concurrent message processing |
| `PrefetchCount` | int | 0 | Number of messages to prefetch |
| `MaxWaitTime` | TimeSpan | "00:00:30" | Max wait time for messages |
| `AutoCompleteMessages` | bool | true | Auto-complete successful messages |
| `MaxDeliveryCount` | int | 3 | Max attempts before dead letter |
| `EnableSessions` | bool | false | Enable session-based processing |

*Either `QueueName` or both `TopicName` and `SubscriptionName` must be specified.

### API Client Settings

Configures HTTP client for destination API calls.

```json
{
  "ApiClient": {
    "BaseUrl": "https://your-api.com",
    "Timeout": "00:00:30",
    "RetryCount": 3,
    "RetryDelay": "00:00:02",
    "DefaultHeaders": {
      "User-Agent": "Sub.JakeCRM.ServiceHost/1.0"
    },
    "ApiKey": null,
    "ApiKeyHeaderName": "X-API-Key",
    "BearerToken": null,
    "EnableDetailedLogging": false,
    "MaxLogContentLength": 4096
  }
}
```

| Setting | Type | Default | Description |
|---------|------|---------|-------------|
| `BaseUrl` | string | **Required** | Destination API base URL |
| `Timeout` | TimeSpan | "00:00:30" | Request timeout |
| `RetryCount` | int | 3 | Number of retry attempts |
| `RetryDelay` | TimeSpan | "00:00:02" | Delay between retries |
| `DefaultHeaders` | object | {} | Default HTTP headers |
| `ApiKey` | string | null | API key for authentication |
| `ApiKeyHeaderName` | string | "X-API-Key" | API key header name |
| `BearerToken` | string | null | Bearer token for auth |
| `EnableDetailedLogging` | bool | false | Log request/response details |
| `MaxLogContentLength` | int | 4096 | Max content length to log |

### Processing Settings

Controls message processing behavior and features.

```json
{
  "Processing": {
    "EnableMessageTransformation": false,
    "TransformationRules": [],
    "EnableMessageFiltering": false,
    "FilterRules": [],
    "EnableBatching": false,
    "MaxBatchSize": 10,
    "BatchTimeoutSeconds": 30,
    "EnableDeduplication": false,
    "DeduplicationCacheSize": 10000,
    "DeduplicationCacheExpiryMinutes": 60,
    "EnableArchiving": false,
    "ArchiveConnectionString": null,
    "ArchiveContainer": "processed-messages",
    "ProcessingTimeoutSeconds": 300,
    "EnableParallelProcessing": true,
    "MaxDegreeOfParallelism": 4
  }
}
```

#### Transformation Rules

Transform message content before API submission:

```json
{
  "TransformationRules": [
    {
      "Name": "MapCustomerIdToClientId",
      "SourcePath": "customerId",
      "TargetPath": "clientId",
      "TransformationType": "Copy",
      "TransformationExpression": null,
      "Enabled": true
    }
  ]
}
```

#### Filter Rules

Filter messages based on content or properties:

```json
{
  "FilterRules": [
    {
      "Name": "OnlyActiveCustomers",
      "FieldPath": "status",
      "Operator": "Equals",
      "Value": "active",
      "Action": "Include",
      "TargetRoute": null,
      "Enabled": true
    }
  ]
}
```

### Monitoring Settings

Configures observability and monitoring features.

```json
{
  "Monitoring": {
    "EnableApplicationInsights": false,
    "ApplicationInsightsKey": null,
    "EnableCustomMetrics": true,
    "MetricsIntervalSeconds": 60,
    "EnablePerformanceCounters": true,
    "EnableDistributedTracing": false,
    "TracingEndpoint": null,
    "EnableStructuredLogging": true,
    "EnableAlerting": false,
    "AlertWebhookUrl": null,
    "HealthChecks": {
      "Enabled": true,
      "Path": "/health",
      "TimeoutSeconds": 30
    }
  }
}
```

### Security Settings

Configures security features and authentication.

```json
{
  "Security": {
    "EnableEncryption": false,
    "EncryptionKey": null,
    "EnableApiAuthentication": true,
    "ApiAuthenticationType": "ApiKey",
    "EnableKeyVault": false,
    "EnableAuditLogging": true,
    "EnableDataMasking": true,
    "MaskedFields": ["password", "secret", "token"],
    "EnableIpFiltering": false,
    "AllowedIpAddresses": [],
    "EnableRateLimiting": false
  }
}
```

## Environment Variables

Override any configuration setting using environment variables with the format:

```
SectionName__SettingName=value
```

Examples:

```bash
# Service Bus settings
ServiceBus__ConnectionString="Endpoint=sb://..."
ServiceBus__QueueName="production-queue"

# API Client settings
ApiClient__BaseUrl="https://api.production.com"
ApiClient__ApiKey="prod-api-key"

# Application settings
Application__Environment="Production"
Application__MaxMessagesPerMinute="1000"
```

## Configuration Validation

The application validates configuration on startup and will fail to start if required settings are missing or invalid. Check the logs for validation errors.

### Required Settings

- `ServiceBus.ConnectionString`
- `ServiceBus.QueueName` OR (`ServiceBus.TopicName` AND `ServiceBus.SubscriptionName`)
- `ApiClient.BaseUrl`

### Common Validation Errors

1. **Missing Service Bus connection string**
   ```
   ServiceBus.ConnectionString is required
   ```

2. **Invalid API base URL**
   ```
   ApiClient.BaseUrl must be a valid URL
   ```

3. **Invalid timeout values**
   ```
   ApiClient.Timeout must be greater than zero
   ```

## Configuration Examples

### Development Environment

```json
{
  "Application": {
    "Environment": "Development",
    "EnableDetailedLogging": true
  },
  "ServiceBus": {
    "ConnectionString": "Endpoint=sb://dev-namespace.servicebus.windows.net/...",
    "QueueName": "dev-queue",
    "MaxConcurrentCalls": 1
  },
  "ApiClient": {
    "BaseUrl": "https://api-dev.jakecrm.com",
    "EnableDetailedLogging": true
  }
}
```

### Production Environment

```json
{
  "Application": {
    "Environment": "Production",
    "EnablePerformanceMonitoring": true,
    "MaxMessagesPerMinute": 1000
  },
  "ServiceBus": {
    "ConnectionString": "Endpoint=sb://prod-namespace.servicebus.windows.net/...",
    "QueueName": "production-queue",
    "MaxConcurrentCalls": 10,
    "PrefetchCount": 20
  },
  "ApiClient": {
    "BaseUrl": "https://api.jakecrm.com",
    "RetryCount": 5,
    "Timeout": "00:01:00"
  },
  "Processing": {
    "EnableBatching": true,
    "MaxBatchSize": 50,
    "EnableParallelProcessing": true,
    "MaxDegreeOfParallelism": 8
  },
  "Monitoring": {
    "EnableApplicationInsights": true,
    "EnableCustomMetrics": true,
    "EnableAlerting": true
  },
  "Security": {
    "EnableEncryption": true,
    "EnableKeyVault": true,
    "EnableAuditLogging": true
  }
}
```

## Best Practices

1. **Use environment-specific configuration files** for different deployment environments
2. **Store sensitive values in environment variables** or Azure Key Vault
3. **Enable detailed logging in development** but disable in production for performance
4. **Configure appropriate timeouts** based on your API response times
5. **Use batching and parallel processing** for high-throughput scenarios
6. **Enable monitoring and alerting** in production environments
7. **Validate configuration** before deployment using the built-in validation
