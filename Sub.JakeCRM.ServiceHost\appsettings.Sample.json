{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Azure.Messaging.ServiceBus": "Warning", "System.Net.Http.HttpClient": "Warning", "Sub.JakeCRM": "Debug"}}, "Application": {"Name": "Sub.JakeCRM.ServiceHost", "Version": "1.0.0", "Environment": "Production", "EnablePerformanceMonitoring": true, "EnableDetailedLogging": false, "MaxMessagesPerMinute": 1000, "ShutdownTimeoutSeconds": 30, "EnableHealthChecks": true, "HealthCheckIntervalSeconds": 30, "CustomProperties": {"DeploymentRegion": "East US", "ServiceTier": "Premium", "DataCenter": "Azure-EastUS-01", "SupportContact": "<EMAIL>"}}, "ServiceBus": {"ConnectionString": "Endpoint=sb://your-servicebus-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-shared-access-key", "QueueName": "crm-messages"}, "ApiClient": {"BaseUrl": "https://api.jakecrm.com", "Timeout": "00:01:00", "RetryCount": 3, "RetryDelay": "00:00:05", "DefaultHeaders": {"User-Agent": "Sub.JakeCRM.ServiceHost/1.0", "Accept": "application/json", "X-Client-Version": "1.0.0"}, "ApiKey": "your-api-key-here", "ApiKeyHeaderName": "X-API-Key", "BearerToken": null, "EnableDetailedLogging": true, "MaxLogContentLength": 8192}, "Processing": {"EnableMessageTransformation": true, "TransformationRules": [{"Name": "MapCustomerIdToClientId", "SourcePath": "customerId", "TargetPath": "clientId", "TransformationType": "Copy", "TransformationExpression": null, "Enabled": true}, {"Name": "AddTimestamp", "SourcePath": "", "TargetPath": "processedAt", "TransformationType": "Transform", "TransformationExpression": "DateTime.UtcNow", "Enabled": true}], "EnableMessageFiltering": true, "FilterRules": [{"Name": "OnlyActiveCustomers", "FieldPath": "status", "Operator": "Equals", "Value": "active", "Action": "Include", "TargetRoute": null, "Enabled": true}, {"Name": "RouteHighPriorityMessages", "FieldPath": "priority", "Operator": "Equals", "Value": "high", "Action": "Route", "TargetRoute": "/api/priority-messages", "Enabled": true}], "EnableBatching": true, "MaxBatchSize": 25, "BatchTimeoutSeconds": 60, "EnableDeduplication": true, "DeduplicationCacheSize": 50000, "DeduplicationCacheExpiryMinutes": 120, "EnableArchiving": true, "ArchiveConnectionString": "DefaultEndpointsProtocol=https;AccountName=yourstorageaccount;AccountKey=your-key;EndpointSuffix=core.windows.net", "ArchiveContainer": "processed-messages", "ProcessingTimeoutSeconds": 300, "EnableParallelProcessing": true, "MaxDegreeOfParallelism": 8}, "Monitoring": {"EnableApplicationInsights": true, "ApplicationInsightsKey": "your-app-insights-key", "ApplicationInsightsConnectionString": "InstrumentationKey=your-key;IngestionEndpoint=https://eastus-8.in.applicationinsights.azure.com/", "EnableCustomMetrics": true, "MetricsIntervalSeconds": 30, "EnablePerformanceCounters": true, "PerformanceCounters": ["MessagesProcessed", "MessagesPerSecond", "ProcessingDuration", "ErrorRate", "ApiResponseTime", "BatchSize", "DeduplicationHitRate"], "EnableDistributedTracing": true, "TracingEndpoint": "http://jaeger-collector:14268/api/traces", "TracingServiceName": "Sub.JakeCRM.ServiceHost", "EnableStructuredLogging": true, "CorrelationIdHeaderName": "X-Correlation-ID", "EnableAlerting": true, "AlertWebhookUrl": "https://hooks.slack.com/services/your/webhook/url", "AlertErrorRateThreshold": 2.0, "AlertLatencyThresholdMs": 3000, "HealthChecks": {"Enabled": true, "Path": "/health", "DetailedPath": "/health/detailed", "TimeoutSeconds": 30, "IncludeDependencies": true, "Dependencies": [{"Name": "ServiceBus", "Type": "ServiceBus", "ConnectionString": "UseMainConnectionString", "TimeoutSeconds": 15, "IsCritical": true, "Parameters": {"QueueName": "crm-messages"}}, {"Name": "CRM_API", "Type": "Api", "ConnectionString": "https://api.jakecrm.com/health", "TimeoutSeconds": 10, "IsCritical": true, "Parameters": {"ExpectedStatusCode": "200"}}, {"Name": "ArchiveStorage", "Type": "Storage", "ConnectionString": "UseArchiveConnectionString", "TimeoutSeconds": 10, "IsCritical": false, "Parameters": {"ContainerName": "processed-messages"}}]}}, "Security": {"EnableEncryption": true, "EncryptionKey": "your-encryption-key-or-keyvault-reference", "EncryptionAlgorithm": "AES256", "EnableMessageSigning": true, "SigningKey": "your-signing-key-or-certificate-reference", "EnableApiAuthentication": true, "ApiAuthenticationType": "<PERSON><PERSON><PERSON><PERSON>", "EnableCertificateAuthentication": false, "ClientCertificate": {"StoreLocation": "CurrentUser", "StoreName": "My", "Thumbprint": "your-certificate-thumbprint", "SubjectName": null, "FilePath": null, "Password": null}, "EnableKeyVault": true, "KeyVault": {"Url": "https://your-keyvault.vault.azure.net/", "ClientId": null, "ClientSecret": null, "TenantId": null, "UseManagedIdentity": true, "SecretNames": ["ServiceBusConnectionString", "<PERSON><PERSON><PERSON><PERSON>", "EncryptionKey"], "CacheSecrets": true, "CacheExpiryMinutes": 60}, "EnableAuditLogging": true, "AuditLogRetentionDays": 365, "EnableDataMasking": true, "MaskedFields": ["password", "secret", "token", "key", "ssn", "creditCard", "email", "phone"], "EnableIpFiltering": true, "AllowedIpAddresses": ["10.0.0.0/8", "**********/12", "***********/16", "***********/24"], "EnableRateLimiting": true, "RateLimiting": {"MaxRequests": 1000, "TimeWindowSeconds": 60, "Strategy": "SlidingWindow", "EnablePerClientLimiting": true, "ClientIdentificationMethod": "<PERSON><PERSON><PERSON><PERSON>"}}}