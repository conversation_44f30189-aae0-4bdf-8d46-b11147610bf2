using Sub.JakeCRM.Domain.Entities;

namespace Sub.JakeCRM.Tests;

public class CustomerTests
{
    [Fact]
    public void Customer_WithValidData_ShouldBeValid()
    {
        // Arrange
        var customer = new Customer
        {
            Id = 123,
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>"
        };

        // Act
        var isValid = customer.IsValid();

        // Assert
        Assert.True(isValid);
        Assert.Equal("John Doe", customer.FullName);
    }

    [Fact]
    public void Customer_WithInvalidId_ShouldBeInvalid()
    {
        // Arrange
        var customer = new Customer
        {
            Id = 0, // Invalid ID
            FirstName = "John",
            LastName = "Doe",
            Email = "<EMAIL>"
        };

        // Act
        var isValid = customer.IsValid();

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void Customer_WithEmptyFirstName_ShouldBeInvalid()
    {
        // Arrange
        var customer = new Customer
        {
            Id = 123,
            FirstName = "", // Empty first name
            LastName = "Doe",
            Email = "<EMAIL>"
        };

        // Act
        var isValid = customer.IsValid();

        // Assert
        Assert.False(isValid);
    }

    [Fact]
    public void Customer_WithInvalidEmail_ShouldBeInvalid()
    {
        // Arrange
        var customer = new Customer
        {
            Id = 123,
            FirstName = "John",
            LastName = "Doe",
            Email = "invalid-email" // Invalid email format
        };

        // Act
        var isValid = customer.IsValid();

        // Assert
        Assert.False(isValid);
    }

    [Theory]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("<EMAIL>", true)]
    [InlineData("invalid-email", false)]
    [InlineData("@example.com", false)]
    [InlineData("user@", false)]
    [InlineData("", false)]
    public void Customer_EmailValidation_ShouldWorkCorrectly(string email, bool expectedValid)
    {
        // Arrange
        var customer = new Customer
        {
            Id = 123,
            FirstName = "John",
            LastName = "Doe",
            Email = email
        };

        // Act
        var isValid = customer.IsValid();

        // Assert
        Assert.Equal(expectedValid, isValid);
    }
}
