{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information", "Azure.Messaging.ServiceBus": "Warning", "System.Net.Http.HttpClient": "Warning"}}, "NLog": {"AutoReload": true, "InternalLogLevel": "Info", "EnableInternalLogging": true, "InternalLogFile": "logs/internal-nlog.txt", "LogDirectory": "logs", "MaxArchiveFiles": 30, "ArchiveEvery": "Day", "EnableConsoleLogging": true, "EnableFileLogging": true, "EnableEventLogLogging": false, "EnableJsonLogging": false, "EnableDatabaseLogging": false, "DatabaseConnectionString": null, "EnablePerformanceCounters": false, "ConsoleMinLevel": "Info", "FileMinLevel": "Info", "EventLogMinLevel": "Error", "JsonMinLevel": "Info", "EnableConcurrentWrites": true, "KeepFileOpen": false, "CustomLayout": null, "EnableCompression": false, "FileEncoding": "UTF-8", "FileBufferSize": 32768, "FlushTimeoutMs": 1000, "EnableAsyncLogging": false, "AsyncQueueLimit": 10000, "AsyncOverflowAction": "Block", "CustomTargets": [], "LoggerRules": []}, "Application": {"Name": "Sub.JakeCRM.ServiceHost", "Version": "1.0.0", "Environment": "Production", "ShutdownTimeoutSeconds": 30}, "ServiceBus": {"ConnectionString": "Endpoint=sb://your-servicebus-namespace.servicebus.windows.net/;SharedAccessKeyName=RootManageSharedAccessKey;SharedAccessKey=your-shared-access-key", "QueueName": "your-queue-name"}, "ApiClient": {"BaseUrl": "https://your-destination-api.com", "Timeout": "00:00:30", "RetryCount": 3, "RetryDelay": "00:00:02", "DefaultHeaders": {"User-Agent": "Sub.JakeCRM.ServiceHost/1.0"}, "ApiKey": null, "ApiKeyHeaderName": "X-API-Key", "BearerToken": null, "EnableDetailedLogging": false, "MaxLogContentLength": 4096}, "Processing": {"ProcessingTimeoutSeconds": 300}, "Monitoring": {"HealthChecks": {"Enabled": true, "Path": "/health", "DetailedPath": "/health/detailed", "TimeoutSeconds": 30, "IncludeDependencies": true, "Dependencies": [{"Name": "ServiceBus", "Type": "ServiceBus", "ConnectionString": "UseMainConnectionString", "TimeoutSeconds": 10, "IsCritical": true, "Parameters": {}}, {"Name": "DestinationAPI", "Type": "Api", "ConnectionString": "UseMainBaseUrl", "TimeoutSeconds": 10, "IsCritical": true, "Parameters": {}}]}}, "Security": {"EnableDataMasking": true, "MaskedFields": ["password", "secret", "token", "key", "ssn", "creditCard"]}}