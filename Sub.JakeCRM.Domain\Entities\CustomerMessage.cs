using System.Text.Json.Serialization;

namespace Sub.JakeCRM.Domain.Entities;

/// <summary>
/// Represents a customer message received from Service Bus
/// </summary>
public class CustomerMessage
{
    /// <summary>
    /// Customer ID
    /// </summary>
    [JsonPropertyName("id")]
    public int Id { get; set; }

    /// <summary>
    /// Customer's first name
    /// </summary>
    [JsonPropertyName("firstName")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Customer's last name  
    /// </summary>
    [JsonPropertyName("lastName")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Customer's email address
    /// </summary>
    [JsonPropertyName("email")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// Converts the message to a Customer domain entity
    /// </summary>
    /// <returns>Customer entity</returns>
    public Customer ToCustomer()
    {
        return new Customer
        {
            Id = Id,
            FirstName = FirstName,
            LastName = LastName,
            Email = Email
        };
    }

    /// <summary>
    /// Validates if the customer message data is complete
    /// </summary>
    /// <returns>True if valid, false otherwise</returns>
    public bool IsValid()
    {
        return Id > 0 && 
               !string.IsNullOrWhiteSpace(FirstName) && 
               !string.IsNullOrWhiteSpace(LastName) && 
               !string.IsNullOrWhiteSpace(Email);
    }
}
