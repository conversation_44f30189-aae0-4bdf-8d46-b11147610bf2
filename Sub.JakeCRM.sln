﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.13.35931.197
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sub.JakeCRM.Domain", "Sub.JakeCRM.Domain\Sub.JakeCRM.Domain.csproj", "{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sub.JakeCRM.Application", "Sub.JakeCRM.Application\Sub.JakeCRM.Application.csproj", "{058579F3-74CF-444E-B3DB-0000009B6A00}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sub.JakeCRM.Infrastructure", "Sub.JakeCRM.Infrastructure\Sub.JakeCRM.Infrastructure.csproj", "{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sub.JakeCRM.ServiceHost", "Sub.JakeCRM.ServiceHost\Sub.JakeCRM.ServiceHost.csproj", "{6C541FFF-6E70-446E-97FA-0C1956686991}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Sub.JakeCRM.Tests", "Sub.JakeCRM.Tests\Sub.JakeCRM.Tests.csproj", "{6524C859-7C83-463E-9670-31C1156AE441}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|x64.ActiveCfg = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|x64.Build.0 = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|x86.ActiveCfg = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Debug|x86.Build.0 = Debug|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|Any CPU.Build.0 = Release|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|x64.ActiveCfg = Release|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|x64.Build.0 = Release|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|x86.ActiveCfg = Release|Any CPU
		{F3C8908B-AC9E-49A7-B7E1-90A28EA391BC}.Release|x86.Build.0 = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|x64.ActiveCfg = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|x64.Build.0 = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|x86.ActiveCfg = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Debug|x86.Build.0 = Debug|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|Any CPU.Build.0 = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|x64.ActiveCfg = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|x64.Build.0 = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|x86.ActiveCfg = Release|Any CPU
		{058579F3-74CF-444E-B3DB-0000009B6A00}.Release|x86.Build.0 = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|x64.Build.0 = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Debug|x86.Build.0 = Debug|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|Any CPU.Build.0 = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|x64.ActiveCfg = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|x64.Build.0 = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|x86.ActiveCfg = Release|Any CPU
		{78D6F4B2-9C8D-4AAD-83D4-94BD807B96FE}.Release|x86.Build.0 = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|x64.Build.0 = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Debug|x86.Build.0 = Debug|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|Any CPU.Build.0 = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|x64.ActiveCfg = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|x64.Build.0 = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|x86.ActiveCfg = Release|Any CPU
		{6C541FFF-6E70-446E-97FA-0C1956686991}.Release|x86.Build.0 = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|x64.ActiveCfg = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|x64.Build.0 = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Debug|x86.Build.0 = Debug|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|Any CPU.Build.0 = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|x64.ActiveCfg = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|x64.Build.0 = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|x86.ActiveCfg = Release|Any CPU
		{6524C859-7C83-463E-9670-31C1156AE441}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {3DA5ABFB-A739-49DD-9C46-2466C4604E6B}
	EndGlobalSection
EndGlobal
